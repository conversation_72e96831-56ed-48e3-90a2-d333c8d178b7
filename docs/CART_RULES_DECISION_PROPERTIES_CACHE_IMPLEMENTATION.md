# Cart Rules Decision Properties Cache Implementation

## Overview

This document describes the implementation of caching for Input Rule Decision Properties and Output Rule Decision Properties in the get-cart-rules-recommendations API. The caching system uses Spring Cache with composite keys to efficiently store and retrieve rule decision properties.

## Cache Architecture

### Cache Names
- `inputRuleDecisionPropertiesCache` - Caches InputRulesDroolDecisionProperties objects
- `outputRuleDecisionPropertiesCache` - Caches OutputRulesDroolDecisionProperties objects

### Composite Key Structure
The cache uses composite keys with the following format:
- **Input Rules**: `{ruleId}_input_rule`
- **Output Rules**: `{ruleId}_output_rule`

Examples:
```
1_input_rule    // Input rule with ID 1
5_output_rule   // Output rule with ID 5
10_input_rule   // Input rule with ID 10
```

## Implementation Details

### Cache Configuration

The cache is configured in `CacheConfig.java`:

```java
@Configuration
@EnableCaching
public class CacheConfig {
    
    @Bean
    public CacheManager cacheManager() {
        ConcurrentMapCacheManager cacheManager = new ConcurrentMapCacheManager();
        
        cacheManager.setCacheNames(java.util.Arrays.asList(
            "inputRulesCache",
            "outputRulesCache",
            "inputRuleDecisionPropertiesCache",
            "outputRuleDecisionPropertiesCache"
        ));
        
        cacheManager.setAllowNullValues(false);
        return cacheManager;
    }
}
```

### Cacheable Methods

#### Input Rule Decision Properties Caching
```java
@Cacheable(value = "inputRuleDecisionPropertiesCache", key = "#ruleId + '_input_rule'")
public InputRulesDroolDecisionProperties getCachedInputRuleDecisionProperties(Integer ruleId) {
    // Method implementation - only called on cache miss
    // Creates and executes input rule, returns cached result
}
```

#### Output Rule Decision Properties Caching
```java
@Cacheable(value = "outputRuleDecisionPropertiesCache", key = "#ruleId + '_output_rule'")
public OutputRulesDroolDecisionProperties getCachedOutputRuleDecisionProperties(Integer ruleId) {
    // Method implementation - only called on cache miss
    // Creates and executes output rule, returns cached result
}
```

### Cache Eviction Methods

#### Clear All Input Rule Decision Properties
```java
@CacheEvict(value = "inputRuleDecisionPropertiesCache", allEntries = true)
public void clearAllInputRuleDecisionPropertiesCache()
```

#### Clear All Output Rule Decision Properties
```java
@CacheEvict(value = "outputRuleDecisionPropertiesCache", allEntries = true)
public void clearAllOutputRuleDecisionPropertiesCache()
```

#### Clear All Rule Decision Properties
```java
@CacheEvict(value = {"inputRuleDecisionPropertiesCache", "outputRuleDecisionPropertiesCache"}, allEntries = true)
public void clearAllRuleDecisionPropertiesCache()
```

#### Evict Specific Entries
```java
@CacheEvict(value = "inputRuleDecisionPropertiesCache", key = "#ruleId + '_input_rule'")
public void evictInputRuleDecisionPropertiesFromCache(Integer ruleId)

@CacheEvict(value = "outputRuleDecisionPropertiesCache", key = "#ruleId + '_output_rule'")
public void evictOutputRuleDecisionPropertiesFromCache(Integer ruleId)
```

## Cache Flow

### Input Rule Decision Properties Caching Flow

1. **Method Call**: `getCachedInputRuleDecisionProperties(ruleId)` is called
2. **Cache Check**: Spring checks if result exists in `inputRuleDecisionPropertiesCache` with key `{ruleId}_input_rule`
3. **Cache Hit**: If found, returns cached result without executing method body
4. **Cache Miss**: If not found, executes method body:
   - Creates InputRulesDroolDecisionProperties with rule ID
   - Executes drool container initialization
   - Caches and returns the result
5. **Return**: Returns either cached or newly computed result

### Output Rule Decision Properties Caching Flow

1. **Method Call**: `getCachedOutputRuleDecisionProperties(ruleId)` is called
2. **Cache Check**: Spring checks if result exists in `outputRuleDecisionPropertiesCache` with key `{ruleId}_output_rule`
3. **Cache Hit**: If found, returns cached result without executing method body
4. **Cache Miss**: If not found, executes method body:
   - Creates OutputRulesDroolDecisionProperties with rule ID
   - Executes drool container initialization
   - Caches and returns the result
5. **Return**: Returns either cached or newly computed result

## REST API Endpoints

### Cache Management Endpoints
- **Base Path**: `/api/v1/cache-refresh`

#### Clear Input Rule Decision Properties Cache
- **Endpoint**: `POST /refresh-input-rule-decision-properties-cache`
- **Description**: Clears all cached input rule decision properties
- **Response**: `true` if successful, `false` otherwise

#### Clear Output Rule Decision Properties Cache
- **Endpoint**: `POST /refresh-output-rule-decision-properties-cache`
- **Description**: Clears all cached output rule decision properties
- **Response**: `true` if successful, `false` otherwise

#### Clear All Rule Decision Properties Cache
- **Endpoint**: `POST /refresh-all-rule-decision-properties-cache`
- **Description**: Clears both input and output rule decision properties caches
- **Response**: `true` if successful, `false` otherwise

#### Evict Specific Input Rule
- **Endpoint**: `POST /evict-input-rule-decision-properties-cache?ruleId={ruleId}`
- **Description**: Evicts specific input rule decision properties from cache
- **Parameters**: `ruleId` - Integer rule ID to evict
- **Response**: `true` if successful, `false` otherwise

#### Evict Specific Output Rule
- **Endpoint**: `POST /evict-output-rule-decision-properties-cache?ruleId={ruleId}`
- **Description**: Evicts specific output rule decision properties from cache
- **Parameters**: `ruleId` - Integer rule ID to evict
- **Response**: `true` if successful, `false` otherwise

## Usage in Cart Rules Recommendation API

The caching is automatically integrated into the cart rules recommendation flow:

1. **Cart Rules Execution**: Determines which input rules to run
2. **Input Rules Processing**: For each input rule ID:
   - Calls `getCachedInputRuleDecisionProperties(ruleId)`
   - Returns cached result if available, otherwise executes and caches
3. **Output Rules Processing**: For each output rule ID from input rules:
   - Calls `getCachedOutputRuleDecisionProperties(ruleId)`
   - Returns cached result if available, otherwise executes and caches
4. **Response Generation**: Uses cached rule properties to generate recommendations

## Benefits

1. **Performance Improvement**: Eliminates redundant drool rule executions
2. **Reduced Resource Usage**: Minimizes CPU and memory usage for repeated rule evaluations
3. **Scalability**: Improves API response times under high load
4. **Flexibility**: Supports both bulk and individual cache eviction
5. **Monitoring**: Provides REST endpoints for cache management and debugging

## Cache Key Examples

```
// Input Rules
1_input_rule     // Input rule ID 1
15_input_rule    // Input rule ID 15
100_input_rule   // Input rule ID 100

// Output Rules
5_output_rule    // Output rule ID 5
25_output_rule   // Output rule ID 25
200_output_rule  // Output rule ID 200
```

## Testing

The implementation includes comprehensive unit tests in `CartRulesOrchestrationServiceCacheTest.java` covering:
- Cache hit/miss scenarios
- Individual cache eviction
- Bulk cache clearing
- Composite key validation
- Cache isolation between input and output rules
