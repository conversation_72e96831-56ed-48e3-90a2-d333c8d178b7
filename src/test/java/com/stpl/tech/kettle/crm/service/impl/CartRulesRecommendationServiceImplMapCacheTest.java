package com.stpl.tech.kettle.crm.service.impl;

import com.stpl.tech.kettle.domain.kettle.model.CartRulesDroolDecisionProperties;
import com.stpl.tech.kettle.domain.kettle.model.InputRulesDroolDecisionProperties;
import com.stpl.tech.kettle.domain.kettle.model.OutputRulesDroolDecisionProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CartRulesRecommendationServiceImplMapCacheTest {

    @Mock
    private CartRulesOrchestrationService cartRulesOrchestrationService;

    @InjectMocks
    private CartRulesRecommendationServiceImpl cartRulesRecommendationService;

    private Method createCacheKeyMethod;
    private Method extractInputRuleIdsMethod;
    private Method extractOutputRuleIdsMethod;

    @BeforeEach
    void setUp() throws Exception {
        // Access private methods for testing
        createCacheKeyMethod = CartRulesRecommendationServiceImpl.class
                .getDeclaredMethod("createCacheKey", Integer.class, String.class);
        createCacheKeyMethod.setAccessible(true);

        extractInputRuleIdsMethod = CartRulesRecommendationServiceImpl.class
                .getDeclaredMethod("extractInputRuleIds", CartRulesDroolDecisionProperties.class);
        extractInputRuleIdsMethod.setAccessible(true);

        extractOutputRuleIdsMethod = CartRulesRecommendationServiceImpl.class
                .getDeclaredMethod("extractOutputRuleIds", InputRulesDroolDecisionProperties.class);
        extractOutputRuleIdsMethod.setAccessible(true);
    }

    @Test
    void testCreateCacheKey() throws Exception {
        // Given
        Integer ruleId = 123;
        String code = "input_rule";

        // When
        String result = (String) createCacheKeyMethod.invoke(cartRulesRecommendationService, ruleId, code);

        // Then
        assertEquals("123_input_rule", result);
    }

    @Test
    void testCreateCacheKey_OutputRule() throws Exception {
        // Given
        Integer ruleId = 456;
        String code = "output_rule";

        // When
        String result = (String) createCacheKeyMethod.invoke(cartRulesRecommendationService, ruleId, code);

        // Then
        assertEquals("456_output_rule", result);
    }

    @Test
    void testExtractInputRuleIds() throws Exception {
        // Given
        CartRulesDroolDecisionProperties cartRulesDecision = CartRulesDroolDecisionProperties.builder()
                .inputRulesToBeRun("1_2_3")
                .build();

        // When
        @SuppressWarnings("unchecked")
        List<Integer> result = (List<Integer>) extractInputRuleIdsMethod.invoke(cartRulesRecommendationService, cartRulesDecision);

        // Then
        assertEquals(3, result.size());
        assertTrue(result.contains(1));
        assertTrue(result.contains(2));
        assertTrue(result.contains(3));
    }

    @Test
    void testExtractInputRuleIds_WithEmptyStrings() throws Exception {
        // Given
        CartRulesDroolDecisionProperties cartRulesDecision = CartRulesDroolDecisionProperties.builder()
                .inputRulesToBeRun("1__3_")
                .build();

        // When
        @SuppressWarnings("unchecked")
        List<Integer> result = (List<Integer>) extractInputRuleIdsMethod.invoke(cartRulesRecommendationService, cartRulesDecision);

        // Then
        assertEquals(2, result.size()); // Empty strings should be filtered out
        assertTrue(result.contains(1));
        assertTrue(result.contains(3));
    }

    @Test
    void testExtractInputRuleIds_NullInput() throws Exception {
        // Given
        CartRulesDroolDecisionProperties cartRulesDecision = CartRulesDroolDecisionProperties.builder()
                .inputRulesToBeRun(null)
                .build();

        // When
        @SuppressWarnings("unchecked")
        List<Integer> result = (List<Integer>) extractInputRuleIdsMethod.invoke(cartRulesRecommendationService, cartRulesDecision);

        // Then
        assertEquals(0, result.size());
    }

    @Test
    void testExtractOutputRuleIds() throws Exception {
        // Given
        InputRulesDroolDecisionProperties inputRule = InputRulesDroolDecisionProperties.builder()
                .outputRulesToBeRun("10_20_30")
                .build();

        // When
        @SuppressWarnings("unchecked")
        List<Integer> result = (List<Integer>) extractOutputRuleIdsMethod.invoke(cartRulesRecommendationService, inputRule);

        // Then
        assertEquals(3, result.size());
        assertTrue(result.contains(10));
        assertTrue(result.contains(20));
        assertTrue(result.contains(30));
    }

    // Note: The cache-related methods have been moved to CartRulesOrchestrationService
    // These tests are commented out as they were testing non-existent methods
    // The actual cache testing is done in CartRulesOrchestrationServiceCacheTest

    /*
    @Test
    void testGetCachedInputRule_NotFound() {
        // This test is no longer valid as the method doesn't exist in this service
    }

    @Test
    void testGetCachedOutputRule_NotFound() {
        // This test is no longer valid as the method doesn't exist in this service
    }

    @Test
    void testGetCachedInputRule_NullRuleId() {
        // This test is no longer valid as the method doesn't exist in this service
    }

    @Test
    void testGetCachedOutputRule_NullRuleId() {
        // This test is no longer valid as the method doesn't exist in this service
    }

    @Test
    void testClearRuleDecisionCache() {
        // This test is no longer valid as the method doesn't exist in this service
    }

    @Test
    void testClearInputRulesCache() {
        // This test is no longer valid as the method doesn't exist in this service
    }

    @Test
    void testClearOutputRulesCache() {
        // This test is no longer valid as the method doesn't exist in this service
    }

    @Test
    void testGetCacheStatistics() {
        // This test is no longer valid as the method doesn't exist in this service
    }
    */

    @Test
    void testCacheKeyFormat() throws Exception {
        // Test that cache keys follow the expected format
        
        // Input rule key
        String inputKey = (String) createCacheKeyMethod.invoke(cartRulesRecommendationService, 123, "input_rule");
        assertEquals("123_input_rule", inputKey);
        
        // Output rule key
        String outputKey = (String) createCacheKeyMethod.invoke(cartRulesRecommendationService, 456, "output_rule");
        assertEquals("456_output_rule", outputKey);
    }

    @Test
    void testExtractRuleIds_InvalidFormat() throws Exception {
        // Given - input with invalid number format
        CartRulesDroolDecisionProperties cartRulesDecision = CartRulesDroolDecisionProperties.builder()
                .inputRulesToBeRun("1_abc_3")
                .build();

        // When
        @SuppressWarnings("unchecked")
        List<Integer> result = (List<Integer>) extractInputRuleIdsMethod.invoke(cartRulesRecommendationService, cartRulesDecision);

        // Then - should skip invalid numbers and continue with valid ones
        assertEquals(2, result.size());
        assertTrue(result.contains(1));
        assertTrue(result.contains(3));
    }
}
