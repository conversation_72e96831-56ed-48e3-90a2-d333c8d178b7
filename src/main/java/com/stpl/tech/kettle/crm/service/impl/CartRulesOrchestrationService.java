package com.stpl.tech.kettle.crm.service.impl;

import com.stpl.tech.kettle.crm.service.DroolInitialiserService;
import com.stpl.tech.kettle.domain.kettle.model.CartRulesDroolDecisionProperties;
import com.stpl.tech.kettle.domain.kettle.model.InputRulesDroolDecisionProperties;
import com.stpl.tech.kettle.domain.kettle.model.OutputRulesDroolDecisionProperties;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Cart Rules Orchestration Service
 * Orchestrates the execution of cart rules decision and subsequent output rules
 * Provides a unified interface for cart-based rule processing
 */
@Service
@Log4j2
public class CartRulesOrchestrationService {

    @Autowired
    @Qualifier("cartRulesDroolInitialiser")
    private CartRulesDroolInitialiser cartRulesDroolInitialiser;

    @Autowired
    @Qualifier("inputRulesDroolInitialiser")
    private InputRulesDroolInitialiser inputRulesDroolInitialiser;

    @Autowired
    @Qualifier("outputRulesDroolInitialiser")
    private OutputRulesDroolInitialiser outputRulesDroolInitialiser;

    /**
     * Execute cart rules decision and return the cart rules result with list of matched rules
     *
     * @param cartRulesProperties Cart rules decision properties
     * @param version             Version of the drool files to use
     * @return Cart rules decision result with list of matched cart rules
     */
    public CartRulesDroolDecisionProperties executeCartRulesDecision(
            CartRulesDroolDecisionProperties cartRulesProperties,
            String version) {
        // Step 1: Execute cart rules decision to get list of matched cart rules
        cartRulesDroolInitialiser.initialiseDroolContainer(cartRulesProperties, version);
        return cartRulesProperties;
    }

    /**
     * Execute output rules based on cart rules result
     *
     * @param cartRulesDroolDecisionProperties Cart rules decision result
     * @param version         Version of the drool files to use
     * @return List of executed output rules
     */
    public List<InputRulesDroolDecisionProperties> executeInputRulesFromCartResult(
            CartRulesDroolDecisionProperties cartRulesDroolDecisionProperties,
            String version) {
        if(Objects.isNull(cartRulesDroolDecisionProperties.getInputRulesToBeRun())){
            return null;
        }

        // Get all output rule numbers from all matched cart rules
        List<Integer> allInputRuleNumbers = parseRuleNumbers(cartRulesDroolDecisionProperties.getInputRulesToBeRun());

        // Execute each output rule
        List<InputRulesDroolDecisionProperties> executedRules = allInputRuleNumbers.stream()
                .map(ruleNumber -> executeInputRule(ruleNumber, version))
                .filter(rule -> rule != null)
                .collect(Collectors.toList());

        log.info("Successfully executed {} output rules", executedRules.size());
        return executedRules;
    }


    public List<OutputRulesDroolDecisionProperties> executeOutputRulesFromCartResult(
            InputRulesDroolDecisionProperties inputRulesDroolDecisionProperties,
            String version) {

        if(Objects.nonNull(inputRulesDroolDecisionProperties.getOutputRulesToBeRun())) {
            List<Integer> allOutputRuleNumbers = parseRuleNumbers(inputRulesDroolDecisionProperties.getOutputRulesToBeRun());
            // Execute each output rule
            List<OutputRulesDroolDecisionProperties> executedRules = allOutputRuleNumbers.stream()
                    .map(ruleNumber -> executeOutputRule(ruleNumber, version))
                    .filter(rule -> rule != null)
                    .collect(Collectors.toList());
            log.info("Successfully executed {} output rules", executedRules.size());
            return executedRules;
        }
        return null;
    }

    public InputRulesDroolDecisionProperties executeInputRule(Integer ruleNumber, String version) {
        try {
            // Use cached method which handles cache hit/miss automatically
            InputRulesDroolDecisionProperties inputRule = getCachedInputRuleDecisionProperties(ruleNumber);
            log.info("Successfully executed input rule number: {}", ruleNumber);
            return inputRule;
        } catch (Exception e) {
            log.error("Failed to execute input rule number: {} with error: {}", ruleNumber, e.getMessage());
            return null;
        }
    }

    /**
     * Execute a specific output rule by rule number
     *
     * @param ruleNumber Rule number to execute
     * @param version    Version of the drool files to use
     * @return Executed output rule properties or null if execution failed
     */
    public OutputRulesDroolDecisionProperties executeOutputRule(Integer ruleNumber, String version) {
        try {
            // Use cached method which handles cache hit/miss automatically
            OutputRulesDroolDecisionProperties outputRule = getCachedOutputRuleDecisionProperties(ruleNumber);
            log.info("Successfully executed output rule number: {}", ruleNumber);
            return outputRule;
        } catch (Exception e) {
            log.error("Failed to execute output rule number: {} with error: {}", ruleNumber, e.getMessage());
            return null;
        }
    }

    /**
     * Parse comma-separated rule numbers from string
     *
     * @param outputRulesToRun Comma-separated rule numbers
     * @return List of rule numbers
     */
    private List<Integer> parseRuleNumbers(String outputRulesToRun) {
        return Arrays.stream(outputRulesToRun.split("_"))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(s -> {
                    try {
                        return Integer.parseInt(s);
                    } catch (NumberFormatException e) {
                        log.warn("Invalid rule number format: {}", s);
                        return null;
                    }
                })
                .filter(ruleNumber -> ruleNumber != null)
                .collect(Collectors.toList());
    }

    /**
     * Create output rule properties from rule number
     * This is a placeholder implementation - in reality, you would fetch rule details
     * from your recommendation rule engine configuration or database
     *
     * @param ruleNumber Rule number
     * @return Output rule properties
     */
    private OutputRulesDroolDecisionProperties createOutputRuleFromRuleNumber(Integer ruleNumber) {
        // This is a sample implementation - replace with actual rule configuration lookup
        return OutputRulesDroolDecisionProperties.builder()
                .rulesNumber(ruleNumber)
                .build();
    }

    private InputRulesDroolDecisionProperties createInputRuleFromRuleNumber(Integer ruleNumber) {
        // This is a sample implementation - replace with actual rule configuration lookup
        return InputRulesDroolDecisionProperties.builder()
                .ruleNum(ruleNumber).build();
    }

    /**
     * Get cached input rule decision properties by rule ID
     * Uses composite key with rule ID and constant code "input_rule"
     *
     * @param ruleId Rule ID to fetch from cache
     * @return Cached input rule decision properties or null if not found
     */
    @Cacheable(value = "inputRuleDecisionPropertiesCache", key = "#ruleId + '_input_rule'")
    public InputRulesDroolDecisionProperties getCachedInputRuleDecisionProperties(Integer ruleId) {
        log.info("Cache miss - creating input rule decision properties for rule ID: {}", ruleId);
        // This method will only be called on cache miss
        // Create input rule properties with the rule number
        InputRulesDroolDecisionProperties inputRule = createInputRuleFromRuleNumber(ruleId);

        // Execute the input rule
        inputRulesDroolInitialiser.initialiseDroolContainer(inputRule, "default");

        return inputRule;
    }

    /**
     * Get cached output rule decision properties by rule ID
     * Uses composite key with rule ID and constant code "output_rule"
     *
     * @param ruleId Rule ID to fetch from cache
     * @return Cached output rule decision properties or null if not found
     */
    @Cacheable(value = "outputRuleDecisionPropertiesCache", key = "#ruleId + '_output_rule'")
    public OutputRulesDroolDecisionProperties getCachedOutputRuleDecisionProperties(Integer ruleId) {
        log.info("Cache miss - creating output rule decision properties for rule ID: {}", ruleId);
        // This method will only be called on cache miss
        // Create output rule properties with the rule number
        OutputRulesDroolDecisionProperties outputRule = createOutputRuleFromRuleNumber(ruleId);

        // Execute the output rule
        outputRulesDroolInitialiser.initialiseDroolContainer(outputRule, "default");

        return outputRule;
    }



    /**
     * Evict specific input rule from cache
     *
     * @param ruleId Rule ID to evict from cache
     */
    @CacheEvict(value = "inputRuleDecisionPropertiesCache", key = "#ruleId + '_input_rule'")
    public void evictInputRuleDecisionPropertiesFromCache(Integer ruleId) {
        log.info("Evicting input rule decision properties from cache for rule ID: {}", ruleId);
    }

    /**
     * Evict specific output rule from cache
     *
     * @param ruleId Rule ID to evict from cache
     */
    @CacheEvict(value = "outputRuleDecisionPropertiesCache", key = "#ruleId + '_output_rule'")
    public void evictOutputRuleDecisionPropertiesFromCache(Integer ruleId) {
        log.info("Evicting output rule decision properties from cache for rule ID: {}", ruleId);
    }

    /**
     * Clear all input rule decision properties from cache
     */
    @CacheEvict(value = "inputRuleDecisionPropertiesCache", allEntries = true)
    public void clearAllInputRuleDecisionPropertiesCache() {
        log.info("Clearing all input rule decision properties from cache");
    }

    /**
     * Clear all output rule decision properties from cache
     */
    @CacheEvict(value = "outputRuleDecisionPropertiesCache", allEntries = true)
    public void clearAllOutputRuleDecisionPropertiesCache() {
        log.info("Clearing all output rule decision properties from cache");
    }

    /**
     * Clear all rule decision properties caches
     */
    @CacheEvict(value = {"inputRuleDecisionPropertiesCache", "outputRuleDecisionPropertiesCache"}, allEntries = true)
    public void clearAllRuleDecisionPropertiesCache() {
        log.info("Clearing all rule decision properties caches");
    }
}


