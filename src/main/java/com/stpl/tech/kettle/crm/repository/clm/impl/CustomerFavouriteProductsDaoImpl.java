package com.stpl.tech.kettle.crm.repository.clm.impl;

import com.stpl.tech.common.cache.UnitCache;
import com.stpl.tech.kettle.crm.cache.impl.CrmCacheService;
import com.stpl.tech.kettle.crm.data.kettle.CustomerBrandMapping;
import com.stpl.tech.kettle.crm.repository.clm.CustomerFavouriteProductsDao;
import com.stpl.tech.kettle.crm.repository.kettle.CustomerBrandMappingDao;
import com.stpl.tech.kettle.crm.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.crm.util.AppConstants;
import com.stpl.tech.kettle.crm.util.AppUtils;
import com.stpl.tech.kettle.crm.util.CustomerVistType;
import com.stpl.tech.kettle.crm.util.TransactionUtils;
import com.stpl.tech.kettle.domain.kettle.model.CartRulesDroolDecisionProperties;
import com.stpl.tech.kettle.domain.kettle.model.CustomerEngagementDroolDecisionProperties;
import com.stpl.tech.kettle.domain.kettle.model.CustomerScreenDroolProperties;
import com.stpl.tech.kettle.domain.kettle.model.MembershipSuggestionDroolDecisionProperties;
import com.stpl.tech.kettle.domain.kettle.model.RecommendedProduct;
import com.stpl.tech.kettle.domain.kettle.model.recom.CustomerRecommendationDroolProperties;
import com.stpl.tech.kettle.domain.kettle.model.request.CustomerBasicInfo;
import com.stpl.tech.kettle.domain.master.model.recom.ProductCategory;
import com.stpl.tech.kettle.domain.master.model.recom.RecommendationDayPartV3;
import com.stpl.tech.kettle.domain.master.model.recom.RecommendationReasonType;
import jakarta.persistence.Query;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Repository
@Log4j2
public class CustomerFavouriteProductsDaoImpl extends ClmDataAbstractDaoImpl implements CustomerFavouriteProductsDao {


    @Autowired
    private CrmCacheService cacheService;
    @Autowired
    private EnvironmentProperties environmentProperties;
    @Autowired
    private UnitCache unitCache;
    @Autowired
    private CustomerBrandMappingDao customerBrandMappingDao;


    @Override
    @Transactional(rollbackFor = Exception.class, value = "CLMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Map<Integer, RecommendedProduct> getCustomerFavouriteProducts(Integer customerId, List<Integer> categoryDefaultSeq, boolean exploreMore) {
        try {
            Query query = manager.createNativeQuery("CALL CUSTOMER_FAVOURITE_PRODUCTS(:customerId, :brandId)");
            if(!AppUtils.isProd(environmentProperties.getEnvironmentType())){
                query = manager.createNativeQuery("CALL CUSTOMER_FAVOURITE_PRODUCTS_TEST(:customerId, :brandId)");
            }
            query.setParameter("customerId", customerId);
            query.setParameter("brandId", AppConstants.CHAAYOS_BRAND_ID);
            List<Object[]> data = query.getResultList();
            if (data.isEmpty()) {
                return null;
            } else {
                return getCustomerTopRatedProducts(data.get(0), categoryDefaultSeq, exploreMore);
            }
        } catch (Exception e) {
            log.error("Exception while getting top rated products for customer with customerId :{}", customerId, e);
            return null;
        }
    }

    private Map<Integer, RecommendedProduct> getCustomerTopRatedProducts(Object[] object, List<Integer> categoryDefaultSeq, boolean exploreMore) {//1->HOT, 2->COLD , 3->FOOD, 4->BAKERY, 5->MERCHANDISE, 6->COMBO
        Map<Integer , RecommendedProduct> categoryWiseCustomerFavProducts= new HashMap<>(5);
        int len = exploreMore ? ProductCategory.values().length-1 : ProductCategory.values().length;
        for(int i = 0 ;i<len ;i++){
            String index = "INDEX"+i;
            Integer categoryId = ProductCategory.valueOf(index).getCategoryId();
            Long val = (Long) object[i + 1];
            Integer productId = (val != null) ? ((!AppConstants.DESI_CHAI_PRODUCT_IDS.contains(Math.toIntExact(val)) && !AppConstants.LEMON_GRASS_CHAI_PRODUCT_IDS.contains(Math.toIntExact(val))) ? Math.toIntExact(val) : 0) : 0;
            RecommendedProduct recommendedProduct = RecommendedProduct.builder().productId(productId).recomReason(RecommendationReasonType.CUSTOMER_TOP_RATED_PRODUCTS.name()).categoryId(categoryId).build();
            categoryWiseCustomerFavProducts.put(categoryId, recommendedProduct);
        }
        return categoryWiseCustomerFavProducts;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CLMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CustomerScreenDroolProperties getCustomerScreenDroolProperties(CustomerBasicInfo customerBasicInfo) {
        try {
            Query query = manager.createNativeQuery("CALL CUSTOMER_PROPERTIES_FOR_SCREENS_DROOL_DECISION(:customerId, :brandId)");
            if (!AppUtils.isProd(environmentProperties.getEnvironmentType())) {
                query = manager.createNativeQuery("CALL CUSTOMER_PROPERTIES_FOR_SCREENS_DROOL_DECISION_TEST(:customerId, :brandId)");
            }
            query.setParameter("customerId", customerBasicInfo.getCustomerId());
            query.setParameter("brandId", AppConstants.CHAAYOS_BRAND_ID);
            List<Object[]> data = query.getResultList();
            if (data.isEmpty()) {
                return setCustomerScreenDroolProperties(null, customerBasicInfo);
            }
            return setCustomerScreenDroolProperties(data.get(0), customerBasicInfo);
        } catch (Exception e) {
            log.error("Error while getting customer properties from one view for customer id: {}",
                    customerBasicInfo.getCustomerId(), e);
        }
        return null;
    }

    private CustomerScreenDroolProperties setCustomerScreenDroolProperties(Object[] data, CustomerBasicInfo customerBasicInfo) {
        if(Objects.isNull(data) && Objects.nonNull(customerBasicInfo)){
            if(customerBasicInfo.isNewCustomer()){
                return CustomerScreenDroolProperties.builder().visitsCount(1).loyaltyPointsBalance(Objects.nonNull(customerBasicInfo.getLoyaltyPoints())?customerBasicInfo.getLoyaltyPoints():0).isNewCustomer(AppConstants.YES).build();
            }else{
                return CustomerScreenDroolProperties.builder().isNewCustomer(AppConstants.NO).eligibleForSignUpOffer(AppConstants.getValue(customerBasicInfo.isEligibleForSignUpOffer())).visitsCount(customerBasicInfo.getOrderCount()).loyaltyPointsBalance(customerBasicInfo.getLoyaltyPoints())
                        .daysSinceCustomerAcquired((AppUtils.getActualDayDifference(customerBasicInfo.getCustomerAcquiredTime(), AppUtils.getCurrentDate())))
                        .visitsCount(Objects.isNull(customerBasicInfo.getOrderCount())? 0: customerBasicInfo.getOrderCount()).build();
            }
        }
        return CustomerScreenDroolProperties.builder()
                .dineInFlag(Integer.parseInt(String.valueOf(data[0])))
                .deliveryFlag(Integer.parseInt(String.valueOf(data[1])))
                .visitsCount(Objects.isNull(customerBasicInfo.getOrderCount())? 0: customerBasicInfo.getOrderCount())
                .dineInVisitsCount(Integer.parseInt(String.valueOf(data[3])))
                .visitsCount30Days(Integer.parseInt(String.valueOf(data[4])))
                .visitsCount90Days(Integer.parseInt(String.valueOf(data[5])))
                .netSales(Integer.parseInt(String.valueOf(data[6])))
                .latestOrderDayDiff(Objects.nonNull(data[7]) ? AppUtils.getActualDayDifference(
                        AppUtils.getDate(String.valueOf(data[7]), AppUtils.DATE_FORMAT_STRING),
                        AppUtils.getCurrentDate()) : -1000000)
                .giftCardBalance(customerBasicInfo.getGiftCardBalance())
                .subscriptionExpiryDayDiff(Objects.nonNull(data[9]) ? AppUtils.getActualDayDifference(AppUtils.getCurrentDate(),
                        AppUtils.getDate(String.valueOf(data[9]), AppUtils.DATE_FORMAT_STRING)) : -1000000)
                .loyaltyPointsBalance(customerBasicInfo.getLoyaltyPoints())
                .isNewCustomer(AppConstants.NO)
                .storeFormat(customerBasicInfo.getStoreFormat())
                .daysSinceCustomerAcquired(AppUtils.getActualDayDifference(customerBasicInfo.getCustomerAcquiredTime(), AppUtils.getCurrentDate()))
                .activeSelectMembership(AppUtils.setStatus(customerBasicInfo.isActiveSelectMemberShip()))
                .eligibleForSignUpOffer(AppUtils.setStatus(customerBasicInfo.isEligibleForSignUpOffer()))
                .visitTimeBetweenThreshold(TransactionUtils.checkWithinTripThresholdTime(customerBasicInfo.getLastVisitTime(),cacheService))
                .build();
    }



    @Override
    @Transactional(rollbackFor = Exception.class, value = "CLMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CustomerRecommendationDroolProperties getCustomerRecomDroolProperties(Integer customerId, boolean newCustomer) {
        try {
            if(Objects.nonNull(customerId) && customerId > 5) {
                Query query = manager.createNativeQuery("CALL CUSTOMER_PROPERTIES_FOR_RECOM_DROOL_DECISION(:customerId, :brandId)");
                query.setParameter("customerId", customerId);
                query.setParameter("brandId", AppConstants.CHAAYOS_BRAND_ID);
                List<Object[]> data = query.getResultList();
                if (data.isEmpty()) {
                    return setCustomerRecomDroolProperties(null, customerId, newCustomer);
                }
                return setCustomerRecomDroolProperties(data.get(0), customerId, newCustomer);
            }
            return setCustomerRecomDroolProperties(null, customerId, newCustomer);
        } catch (Exception e) {
            log.error("Error while getting customer properties for recom drool decision for customer id: {}",
                    customerId, e);
        }
        return null;
    }

    private CustomerRecommendationDroolProperties setCustomerRecomDroolProperties(Object[] data, Integer customerId, boolean newCustomer) {
        if (Objects.isNull(data) && Objects.nonNull(customerId)) {
            if (newCustomer) {
                return CustomerRecommendationDroolProperties.builder().isNewCustomer(AppConstants.YES).build();
            } else {
                return CustomerRecommendationDroolProperties.builder().isNewCustomer(AppConstants.NO).build();
            }
        }
        return CustomerRecommendationDroolProperties.builder()
                .isNewCustomer(AppConstants.NO)
                .custVisitType(String.valueOf(data[0]))
                .dineInFoodClassPreference(String.valueOf(data[1]))
                .numberOfPax(BigDecimal.valueOf(Double.parseDouble(String.valueOf(data[2]))))
                .APC_BUCKET(String.valueOf(data[3]))
                .customerFrequecy(String.valueOf(data[11]))
                .build();
    }



    @Override
    @Transactional(rollbackFor = Exception.class, value = "CLMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public CustomerEngagementDroolDecisionProperties getCustomerEngagementDroolDecisionProperties(int customerId, int unitId) {
        try {
            Query query = manager.createNativeQuery("CALL CUSTOMER_PROPERTIES_FOR_SCREENS_DROOL_DECISION(:customerId, :brandId)");
            if (!AppUtils.isProd(environmentProperties.getEnvironmentType())) {
                query = manager.createNativeQuery("CALL CUSTOMER_PROPERTIES_FOR_SCREENS_DROOL_DECISION_TEST(:customerId, :brandId)");
            }
            query.setParameter("customerId", customerId);
            query.setParameter("brandId", AppConstants.CHAAYOS_BRAND_ID);
            List<Object[]> data = query.getResultList();
            if (data.isEmpty()) {
                return setCustomerEngagementDroolDecisionProperties(null,unitId, customerId);
             }
            return setCustomerEngagementDroolDecisionProperties(data.get(0), unitId, customerId);
        } catch (Exception e) {
            log.error("Error while getting customer properties from one view for customer id: {}",
                    customerId, e);
        }
        return null;
    }

    private CustomerEngagementDroolDecisionProperties setCustomerEngagementDroolDecisionProperties(Object[] data, int unitId, Integer customerId) {
        CustomerEngagementDroolDecisionProperties.CustomerEngagementDroolDecisionPropertiesBuilder customerEngagementDroolDecisionProperties = CustomerEngagementDroolDecisionProperties.builder();
        customerEngagementDroolDecisionProperties.isNewCustomer("N");
        if (Objects.isNull(data)) {
            customerEngagementDroolDecisionProperties.customerType("DEFAULT");
        } else {
            String [] segment = String.valueOf(data[11]).replaceAll("\\s+", "").split("\\.");
            customerEngagementDroolDecisionProperties.customerType(segment[1]);
        }
        CustomerBrandMapping customerBrandMappings = customerBrandMappingDao.findByCustomerIdAndBrandId(customerId,AppConstants.CHAAYOS_BRAND_ID);
        if(Objects.isNull(customerBrandMappings) || (Objects.nonNull(customerBrandMappings) && customerBrandMappings.getTotalOrder() == 0)){
            customerEngagementDroolDecisionProperties
                    .customerType("NEW");
            customerEngagementDroolDecisionProperties.isNewCustomer("Y");
        }
        customerEngagementDroolDecisionProperties
                .unitId(String.valueOf(unitId))
                .unitZone(unitCache.getUnitById(unitId).getUnitZone())
                .unitCategory(unitCache.getUnitById(unitId).getSubCategory().value())
                .unitCity(unitCache.getUnitById(unitId).getRegion())
                .daypart(RecommendationDayPartV3.getCurrentDayPart(AppUtils.getCurrentTimestamp()).name())
                ;

        return customerEngagementDroolDecisionProperties.build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CLMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public MembershipSuggestionDroolDecisionProperties getMemberShipSuggestionDroolDecisionProperties(int customerId, int unitId) {
        try {
            Query query = manager.createNativeQuery("CALL CUSTOMER_PROPERTIES_FOR_SCREENS_DROOL_DECISION(:customerId, :brandId)");
            if (!AppUtils.isProd(environmentProperties.getEnvironmentType())) {
                query = manager.createNativeQuery("CALL CUSTOMER_PROPERTIES_FOR_SCREENS_DROOL_DECISION_TEST(:customerId, :brandId)");
            }
            query.setParameter("customerId", customerId);
            query.setParameter("brandId", AppConstants.CHAAYOS_BRAND_ID);
            List<Object[]> data = query.getResultList();
            if (data.isEmpty()) {
                return setMembershipSuggestionDroolDecisionProperties(null,unitId, customerId);
            }
            return setMembershipSuggestionDroolDecisionProperties(data.get(0), unitId, customerId);
        } catch (Exception e) {
            log.error("Error while getting customer properties from one view for customer id: {}",
                    customerId, e);
        }
        return null;
    }

    private MembershipSuggestionDroolDecisionProperties setMembershipSuggestionDroolDecisionProperties(Object[] data, int unitId, Integer customerId) {
        MembershipSuggestionDroolDecisionProperties.MembershipSuggestionDroolDecisionPropertiesBuilder membershipSuggestionDroolDecisionProperties = MembershipSuggestionDroolDecisionProperties.builder();
        if(Objects.isNull(data)){
            CustomerBrandMapping customerBrandMappings = customerBrandMappingDao.findByCustomerIdAndBrandId(customerId,AppConstants.CHAAYOS_BRAND_ID);
            if(Objects.isNull(customerBrandMappings) || (Objects.nonNull(customerBrandMappings) && customerBrandMappings.getTotalOrder() == 0)){
                return membershipSuggestionDroolDecisionProperties.isNewCustomer("Y").customerVisitType("NEW").build();
            }
        }else if(Objects.nonNull(data[12])){
            if(Objects.nonNull(data[13])){
                membershipSuggestionDroolDecisionProperties.unitCity(unitCache.getUnitById((Integer) data[13]).getRegion());
            }
            String segment = String.valueOf(data[12]);
            if(segment.contains(CustomerVistType.AHF.name())){
               return membershipSuggestionDroolDecisionProperties.isNewCustomer("N").customerVisitType(CustomerVistType.AHF.name()).build();
            }else if(segment.contains(CustomerVistType.ALF.name())){
                return membershipSuggestionDroolDecisionProperties.isNewCustomer("N").customerVisitType(CustomerVistType.ALF.name()).build();
            }else if(segment.contains(CustomerVistType.DORMANT.name())){
                return membershipSuggestionDroolDecisionProperties.isNewCustomer("N").customerVisitType(CustomerVistType.DORMANT.name()).build();
            }else{
                return membershipSuggestionDroolDecisionProperties.isNewCustomer("Y").customerVisitType(CustomerVistType.NEW.name()).build();
            }
        }
        return membershipSuggestionDroolDecisionProperties.isNewCustomer("N").customerVisitType("DEFAULT").build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CLMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public CartRulesDroolDecisionProperties getCartRulesDroolDecisionProperties(int customerId, CartRulesDroolDecisionProperties.CartRulesDroolDecisionPropertiesBuilder cartRulesDroolDecisionProperties) {
        try {
            Query query = manager.createNativeQuery("CALL CUSTOMER_PROPERTIES_FOR_SCREENS_DROOL_DECISION(:customerId, :brandId)");
            if (!AppUtils.isProd(environmentProperties.getEnvironmentType())) {
                query = manager.createNativeQuery("CALL CUSTOMER_PROPERTIES_FOR_SCREENS_DROOL_DECISION_TEST(:customerId, :brandId)");
            }
            query.setParameter("customerId", customerId);
            query.setParameter("brandId", AppConstants.CHAAYOS_BRAND_ID);
            List<Object[]> data = query.getResultList();
            if (data.isEmpty()) {
                return setCartRulesDroolDecisionProperties(null, customerId, cartRulesDroolDecisionProperties);
            }
            return setCartRulesDroolDecisionProperties(data.get(0), customerId, cartRulesDroolDecisionProperties);
        } catch (Exception e) {
            log.error("Error while getting customer properties from one view for customer id: {}",
                    customerId, e);
        }
        return null;
    }

    private CartRulesDroolDecisionProperties setCartRulesDroolDecisionProperties(Object[] data, Integer customerId, CartRulesDroolDecisionProperties.CartRulesDroolDecisionPropertiesBuilder cartRulesDroolDecisionPropertiesBuilder) {
        if(Objects.isNull(data)){

            CustomerBrandMapping customerBrandMappings = customerBrandMappingDao.findByCustomerIdAndBrandId(customerId,AppConstants.CHAAYOS_BRAND_ID);
            if(Objects.isNull(customerBrandMappings) || (Objects.nonNull(customerBrandMappings) && customerBrandMappings.getTotalOrder() == 0)){
                return cartRulesDroolDecisionPropertiesBuilder.isNewCustomer("Y").customerFrequency("NEW").lastOrderCategory("DEFAULT").build();
            }
        }else if(Objects.nonNull(data[12])){
            if(Objects.nonNull(data[14])){
                cartRulesDroolDecisionPropertiesBuilder.lastOrderCategory((String) data[14]);
            } else {
                cartRulesDroolDecisionPropertiesBuilder.lastOrderCategory("DEFAULT");
            }
            String segment = String.valueOf(data[12]);
            if(segment.contains(CustomerVistType.AHF.name())){
                return cartRulesDroolDecisionPropertiesBuilder.isNewCustomer("N").customerFrequency(CustomerVistType.AHF.name()).build();
            }else if(segment.contains(CustomerVistType.ALF.name())){
                return cartRulesDroolDecisionPropertiesBuilder.isNewCustomer("N").customerFrequency(CustomerVistType.ALF.name()).build();
            }else if(segment.contains(CustomerVistType.DORMANT.name())){
                return cartRulesDroolDecisionPropertiesBuilder.isNewCustomer("N").customerFrequency(CustomerVistType.DORMANT.name()).build();
            }else{
                return cartRulesDroolDecisionPropertiesBuilder.isNewCustomer("Y").customerFrequency("DEFAULT").build();
            }
        }
        return cartRulesDroolDecisionPropertiesBuilder.isNewCustomer("N").customerFrequency("DEFAULT").build();
    }

}
