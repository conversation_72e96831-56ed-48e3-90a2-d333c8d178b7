package com.stpl.tech.kettle.crm.controller;

import com.stpl.tech.kettle.crm.cache.BrandDetailCache;
import com.stpl.tech.kettle.crm.service.impl.CartRulesOrchestrationService;
import com.stpl.tech.kettle.crm.util.CustomerServiceConstants;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@Log4j2
@RequestMapping(value = CustomerServiceConstants.API_VERSION + CustomerServiceConstants.SEPARATOR + CustomerServiceConstants.CACHE_REFRESH_RESOURCE)
public class CacheRefreshResource {

    @Autowired
    private BrandDetailCache brandDetailCache;

    @Autowired
    private CartRulesOrchestrationService cartRulesOrchestrationService;

    @PostMapping(value = "refresh-brand-cache")
    public boolean refreshBrandDetailCache() {
        try {
            brandDetailCache.removeBrandDetailCache();
            return true;
        }catch (Exception e){
            log.error("Error while removing brand detail cache");
        }
        return false;
    }

    @PostMapping(value = "refresh-input-rule-decision-properties-cache")
    public boolean refreshInputRuleDecisionPropertiesCache() {
        try {
            cartRulesOrchestrationService.clearAllInputRuleDecisionPropertiesCache();
            log.info("Successfully cleared input rule decision properties cache");
            return true;
        } catch (Exception e) {
            log.error("Error while clearing input rule decision properties cache", e);
        }
        return false;
    }

    @PostMapping(value = "refresh-output-rule-decision-properties-cache")
    public boolean refreshOutputRuleDecisionPropertiesCache() {
        try {
            cartRulesOrchestrationService.clearAllOutputRuleDecisionPropertiesCache();
            log.info("Successfully cleared output rule decision properties cache");
            return true;
        } catch (Exception e) {
            log.error("Error while clearing output rule decision properties cache", e);
        }
        return false;
    }

    @PostMapping(value = "refresh-all-rule-decision-properties-cache")
    public boolean refreshAllRuleDecisionPropertiesCache() {
        try {
            cartRulesOrchestrationService.clearAllRuleDecisionPropertiesCache();
            log.info("Successfully cleared all rule decision properties caches");
            return true;
        } catch (Exception e) {
            log.error("Error while clearing all rule decision properties caches", e);
        }
        return false;
    }

    @PostMapping(value = "evict-input-rule-decision-properties-cache")
    public boolean evictInputRuleDecisionPropertiesCache(@RequestParam Integer ruleId) {
        try {
            cartRulesOrchestrationService.evictInputRuleDecisionPropertiesFromCache(ruleId);
            log.info("Successfully evicted input rule decision properties from cache for rule ID: {}", ruleId);
            return true;
        } catch (Exception e) {
            log.error("Error while evicting input rule decision properties from cache for rule ID: {}", ruleId, e);
        }
        return false;
    }

    @PostMapping(value = "evict-output-rule-decision-properties-cache")
    public boolean evictOutputRuleDecisionPropertiesCache(@RequestParam Integer ruleId) {
        try {
            cartRulesOrchestrationService.evictOutputRuleDecisionPropertiesFromCache(ruleId);
            log.info("Successfully evicted output rule decision properties from cache for rule ID: {}", ruleId);
            return true;
        } catch (Exception e) {
            log.error("Error while evicting output rule decision properties from cache for rule ID: {}", ruleId, e);
        }
        return false;
    }
}
